import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { 
  collection, 
  query, 
  orderBy, 
  onSnapshot, 
  addDoc, 
  updateDoc, 
  doc, 
  where,
  limit,
  startAfter,
  getDocs
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Chat, Message, MessageType, EmotionalTone } from '../types';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';
import { v4 as uuidv4 } from 'uuid';

interface ChatContextType {
  chats: Chat[];
  currentChat: Chat | null;
  messages: Message[];
  loading: boolean;
  sendingMessage: boolean;
  setCurrentChat: (chat: Chat | null) => void;
  sendMessage: (content: string, type?: MessageType, metadata?: any) => Promise<void>;
  createChat: (participants: string[], type: Chat['type'], name?: string) => Promise<Chat>;
  loadMoreMessages: () => Promise<void>;
  markMessageAsRead: (messageId: string) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  removeReaction: (messageId: string, emoji: string) => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  searchMessages: (query: string) => Promise<Message[]>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [lastMessageDoc, setLastMessageDoc] = useState<any>(null);

  // Load user's chats
  useEffect(() => {
    if (!user) return;

    setLoading(true);
    const chatsQuery = query(
      collection(db, 'chats'),
      where('participants', 'array-contains', user.id),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(chatsQuery, (snapshot) => {
      const chatsData: Chat[] = [];
      snapshot.forEach((doc) => {
        chatsData.push({ id: doc.id, ...doc.data() } as Chat);
      });
      setChats(chatsData);
      setLoading(false);
    }, (error) => {
      console.error('Error loading chats:', error);
      toast.error('Failed to load chats');
      setLoading(false);
    });

    return unsubscribe;
  }, [user]);

  // Load messages for current chat
  useEffect(() => {
    if (!currentChat) {
      setMessages([]);
      return;
    }

    const messagesQuery = query(
      collection(db, 'messages'),
      where('chatId', '==', currentChat.id),
      orderBy('timestamp', 'desc'),
      limit(50)
    );

    const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
      const messagesData: Message[] = [];
      snapshot.forEach((doc) => {
        messagesData.push({ id: doc.id, ...doc.data() } as Message);
      });
      
      // Reverse to show oldest first
      messagesData.reverse();
      setMessages(messagesData);
      
      // Set last document for pagination
      if (snapshot.docs.length > 0) {
        setLastMessageDoc(snapshot.docs[snapshot.docs.length - 1]);
      }
    }, (error) => {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    });

    return unsubscribe;
  }, [currentChat]);

  const sendMessage = async (
    content: string, 
    type: MessageType = 'text', 
    metadata: any = {}
  ) => {
    if (!user || !currentChat || !content.trim()) return;

    setSendingMessage(true);
    try {
      const message: Omit<Message, 'id'> = {
        chatId: currentChat.id,
        senderId: user.id,
        content: content.trim(),
        type,
        timestamp: new Date(),
        reactions: [],
        attachments: [],
        metadata: {
          ...metadata,
          readBy: { [user.id]: new Date() },
          deliveredTo: { [user.id]: new Date() },
        },
      };

      // Add message to Firestore
      await addDoc(collection(db, 'messages'), message);

      // Update chat's last message and timestamp
      await updateDoc(doc(db, 'chats', currentChat.id), {
        lastMessage: {
          content: content.trim(),
          senderId: user.id,
          timestamp: new Date(),
          type,
        },
        updatedAt: new Date(),
      });

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  const createChat = async (
    participants: string[], 
    type: Chat['type'], 
    name?: string
  ): Promise<Chat> => {
    if (!user) throw new Error('User not authenticated');

    try {
      const chatData: Omit<Chat, 'id'> = {
        participants: [...participants, user.id],
        type,
        name,
        createdAt: new Date(),
        updatedAt: new Date(),
        settings: {
          emotionalUI: true,
          soundMoodscape: true,
          videoReactions: false,
          puzzleLocks: false,
          messageSpells: false,
          timeCapsules: false,
          heartbeatSync: false,
          stargazerMode: false,
        },
      };

      const docRef = await addDoc(collection(db, 'chats'), chatData);
      const newChat: Chat = { id: docRef.id, ...chatData };
      
      toast.success('Chat created successfully!');
      return newChat;
    } catch (error) {
      console.error('Error creating chat:', error);
      toast.error('Failed to create chat');
      throw error;
    }
  };

  const loadMoreMessages = async () => {
    if (!currentChat || !lastMessageDoc) return;

    try {
      const messagesQuery = query(
        collection(db, 'messages'),
        where('chatId', '==', currentChat.id),
        orderBy('timestamp', 'desc'),
        startAfter(lastMessageDoc),
        limit(50)
      );

      const snapshot = await getDocs(messagesQuery);
      const newMessages: Message[] = [];
      
      snapshot.forEach((doc) => {
        newMessages.push({ id: doc.id, ...doc.data() } as Message);
      });

      if (newMessages.length > 0) {
        // Reverse and prepend to existing messages
        newMessages.reverse();
        setMessages(prev => [...newMessages, ...prev]);
        setLastMessageDoc(snapshot.docs[snapshot.docs.length - 1]);
      }
    } catch (error) {
      console.error('Error loading more messages:', error);
      toast.error('Failed to load more messages');
    }
  };

  const markMessageAsRead = async (messageId: string) => {
    if (!user) return;

    try {
      await updateDoc(doc(db, 'messages', messageId), {
        [`metadata.readBy.${user.id}`]: new Date(),
      });
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const addReaction = async (messageId: string, emoji: string) => {
    if (!user) return;

    try {
      const message = messages.find(m => m.id === messageId);
      if (!message) return;

      const existingReaction = message.reactions.find(
        r => r.userId === user.id && r.emoji === emoji
      );

      if (existingReaction) return; // Already reacted with this emoji

      const newReaction = {
        id: uuidv4(),
        userId: user.id,
        emoji,
        timestamp: new Date(),
      };

      await updateDoc(doc(db, 'messages', messageId), {
        reactions: [...message.reactions, newReaction],
      });
    } catch (error) {
      console.error('Error adding reaction:', error);
      toast.error('Failed to add reaction');
    }
  };

  const removeReaction = async (messageId: string, emoji: string) => {
    if (!user) return;

    try {
      const message = messages.find(m => m.id === messageId);
      if (!message) return;

      const updatedReactions = message.reactions.filter(
        r => !(r.userId === user.id && r.emoji === emoji)
      );

      await updateDoc(doc(db, 'messages', messageId), {
        reactions: updatedReactions,
      });
    } catch (error) {
      console.error('Error removing reaction:', error);
      toast.error('Failed to remove reaction');
    }
  };

  const editMessage = async (messageId: string, newContent: string) => {
    if (!user) return;

    try {
      await updateDoc(doc(db, 'messages', messageId), {
        content: newContent,
        editedAt: new Date(),
      });
      toast.success('Message edited');
    } catch (error) {
      console.error('Error editing message:', error);
      toast.error('Failed to edit message');
    }
  };

  const deleteMessage = async (messageId: string) => {
    if (!user) return;

    try {
      await updateDoc(doc(db, 'messages', messageId), {
        content: '[Message deleted]',
        type: 'system' as MessageType,
        editedAt: new Date(),
      });
      toast.success('Message deleted');
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
    }
  };

  const searchMessages = async (query: string): Promise<Message[]> => {
    if (!currentChat || !query.trim()) return [];

    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation - consider using Algolia or similar for production
      const messagesQuery = collection(db, 'messages');
      const snapshot = await getDocs(messagesQuery);
      
      const searchResults: Message[] = [];
      snapshot.forEach((doc) => {
        const message = { id: doc.id, ...doc.data() } as Message;
        if (
          message.chatId === currentChat.id &&
          message.content.toLowerCase().includes(query.toLowerCase())
        ) {
          searchResults.push(message);
        }
      });

      return searchResults;
    } catch (error) {
      console.error('Error searching messages:', error);
      toast.error('Failed to search messages');
      return [];
    }
  };

  const value: ChatContextType = {
    chats,
    currentChat,
    messages,
    loading,
    sendingMessage,
    setCurrentChat,
    sendMessage,
    createChat,
    loadMoreMessages,
    markMessageAsRead,
    addReaction,
    removeReaction,
    editMessage,
    deleteMessage,
    searchMessages,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};
