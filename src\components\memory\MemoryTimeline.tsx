import React from 'react';
import { motion } from 'framer-motion';
import { ClockIcon, HeartIcon, SparklesIcon } from '@heroicons/react/24/outline';

const MemoryTimeline: React.FC = () => {
  const memories = [
    {
      id: '1',
      title: 'First Message',
      description: 'The beginning of our conversation',
      date: new Date('2024-01-15'),
      type: 'first',
      emotionalScore: 0.8,
    },
    {
      id: '2',
      title: 'Heartfelt Moment',
      description: 'A deep emotional connection was made',
      date: new Date('2024-01-20'),
      type: 'emotional_peak',
      emotionalScore: 0.95,
    },
    {
      id: '3',
      title: 'Funny Exchange',
      description: 'Laughter filled the conversation',
      date: new Date('2024-01-25'),
      type: 'funny',
      emotionalScore: 0.7,
    },
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'first':
        return SparklesIcon;
      case 'emotional_peak':
        return HeartIcon;
      default:
        return ClockIcon;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'first':
        return 'from-yellow-500 to-orange-500';
      case 'emotional_peak':
        return 'from-red-500 to-pink-500';
      case 'funny':
        return 'from-green-500 to-blue-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-display font-bold gradient-text mb-2">
            Memory Timeline
          </h1>
          <p className="text-white/70 text-lg">
            Relive your most precious conversation moments
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500 to-secondary-500" />

          {/* Memory Items */}
          <div className="space-y-8">
            {memories.map((memory, index) => {
              const IconComponent = getTypeIcon(memory.type);
              return (
                <motion.div
                  key={memory.id}
                  className="relative flex items-start space-x-6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  {/* Timeline Icon */}
                  <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${getTypeColor(memory.type)} flex items-center justify-center z-10`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>

                  {/* Memory Card */}
                  <motion.div
                    className="flex-1 glass rounded-xl p-6"
                    whileHover={{ scale: 1.02, y: -5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-white mb-2">
                          {memory.title}
                        </h3>
                        <p className="text-white/70">
                          {memory.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-white/60 text-sm">
                          {memory.date.toLocaleDateString()}
                        </div>
                        <div className="flex items-center mt-1">
                          <HeartIcon className="w-4 h-4 text-red-400 mr-1" />
                          <span className="text-sm text-white/60">
                            {Math.round(memory.emotionalScore * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex space-x-2">
                        <span className="px-3 py-1 bg-primary-500/20 text-primary-300 rounded-full text-sm">
                          {memory.type.replace('_', ' ')}
                        </span>
                      </div>
                      <motion.button
                        className="text-primary-400 hover:text-primary-300 text-sm transition-colors"
                        whileHover={{ scale: 1.05 }}
                      >
                        View Details
                      </motion.button>
                    </div>
                  </motion.div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {memories.length === 0 && (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <ClockIcon className="w-24 h-24 text-white/20 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              No memories yet
            </h3>
            <p className="text-white/60">
              Start chatting to create your first memories
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default MemoryTimeline;
