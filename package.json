{"name": "chat", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/canvas-confetti": "^1.9.0", "@types/howler": "^2.2.12", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "@types/react-textarea-autosize": "^4.3.6", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "canvas-confetti": "^1.9.3", "date-fns": "^4.1.0", "firebase": "^11.8.1", "framer-motion": "^12.16.0", "howler": "^2.2.4", "lucide-react": "^0.513.0", "openai": "^5.1.0", "react": "^19.1.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "react-textarea-autosize": "^8.5.9", "react-webcam": "^7.2.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}