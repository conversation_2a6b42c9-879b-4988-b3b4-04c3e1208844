// User Types
export interface User {
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  personalityType?: PersonalityType;
  preferences: UserPreferences;
  createdAt: Date;
  lastSeen: Date;
  isOnline: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  emotionalMode: boolean;
  soundEnabled: boolean;
  notificationsEnabled: boolean;
  aiMirrorEnabled: boolean;
  dreamChatEnabled: boolean;
  heartbeatSync: boolean;
  location?: {
    lat: number;
    lng: number;
    city?: string;
    country?: string;
  };
}

export type PersonalityType = 
  | 'dreamer' 
  | 'explorer' 
  | 'creator' 
  | 'guardian' 
  | 'sage' 
  | 'rebel' 
  | 'lover' 
  | 'mystic';

// Chat Types
export interface Chat {
  id: string;
  participants: string[];
  type: 'direct' | 'group' | 'ai' | 'diary';
  name?: string;
  description?: string;
  avatar?: string;
  lastMessage?: Message;
  createdAt: Date;
  updatedAt: Date;
  settings: ChatSettings;
  emotionalTone?: EmotionalTone;
  memoryScore?: number;
}

export interface ChatSettings {
  emotionalUI: boolean;
  soundMoodscape: boolean;
  videoReactions: boolean;
  puzzleLocks: boolean;
  messageSpells: boolean;
  timeCapsules: boolean;
  heartbeatSync: boolean;
  stargazerMode: boolean;
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: MessageType;
  timestamp: Date;
  editedAt?: Date;
  replyTo?: string;
  reactions: Reaction[];
  attachments: Attachment[];
  metadata: MessageMetadata;
  spell?: MessageSpell;
  puzzle?: MessagePuzzle;
  timeCapsule?: TimeCapsule;
  emotionalTone?: EmotionalTone;
  aiGenerated?: boolean;
}

export type MessageType = 
  | 'text' 
  | 'image' 
  | 'video' 
  | 'audio' 
  | 'file' 
  | 'location' 
  | 'poll' 
  | 'ai_response' 
  | 'system' 
  | 'time_capsule' 
  | 'puzzle' 
  | 'spell';

export interface MessageMetadata {
  location?: {
    lat: number;
    lng: number;
    address?: string;
  };
  deviceInfo?: string;
  mood?: string;
  weather?: string;
  musicPlaying?: string;
  heartRate?: number;
  readBy: { [userId: string]: Date };
  deliveredTo: { [userId: string]: Date };
}

export interface Reaction {
  id: string;
  userId: string;
  emoji: string;
  timestamp: Date;
  intensity?: number; // 1-10 for emotional intensity
}

export interface Attachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'file' | 'voice_note' | 'video_reaction';
  url: string;
  name: string;
  size: number;
  mimeType: string;
  thumbnail?: string;
  duration?: number; // for audio/video
  transcription?: string; // for audio
}

// Special Message Features
export interface MessageSpell {
  type: 'burn_after_read' | 'freeze' | 'loop' | 'invisible_ink' | 'glow' | 'shake';
  duration?: number;
  intensity?: number;
  activated: boolean;
  activatedAt?: Date;
}

export interface MessagePuzzle {
  type: 'riddle' | 'math' | 'word_game' | 'image_puzzle' | 'sequence';
  question: string;
  answer: string;
  hints: string[];
  attempts: number;
  maxAttempts: number;
  solved: boolean;
  solvedBy?: string;
  solvedAt?: Date;
  reward?: string;
}

export interface TimeCapsule {
  unlockDate: Date;
  unlocked: boolean;
  unlockedAt?: Date;
  previewText?: string;
  significance: 'low' | 'medium' | 'high' | 'milestone';
}

// Emotional Analysis
export interface EmotionalTone {
  primary: EmotionType;
  secondary?: EmotionType;
  intensity: number; // 0-1
  confidence: number; // 0-1
  keywords: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  energy: 'high' | 'medium' | 'low';
}

export type EmotionType = 
  | 'joy' 
  | 'sadness' 
  | 'anger' 
  | 'fear' 
  | 'surprise' 
  | 'disgust' 
  | 'love' 
  | 'excitement' 
  | 'calm' 
  | 'anxiety' 
  | 'hope' 
  | 'nostalgia' 
  | 'curiosity' 
  | 'confusion';

// AI Features
export interface AIResponse {
  id: string;
  type: 'mirror' | 'prediction' | 'tarot' | 'dream_chat' | 'emotion_analysis' | 'parallel_universe';
  content: string;
  confidence: number;
  metadata: any;
  timestamp: Date;
}

export interface TarotReading {
  id: string;
  question: string;
  card: TarotCard;
  interpretation: string;
  timestamp: Date;
  userId: string;
}

export interface TarotCard {
  name: string;
  suit?: string;
  number?: number;
  image: string;
  meaning: {
    upright: string;
    reversed: string;
  };
  keywords: string[];
}

// Memory Features
export interface Memory {
  id: string;
  chatId: string;
  messageIds: string[];
  title: string;
  description?: string;
  type: 'first' | 'milestone' | 'emotional_peak' | 'funny' | 'important' | 'custom';
  emotionalScore: number;
  participants: string[];
  location?: {
    lat: number;
    lng: number;
    address?: string;
  };
  timestamp: Date;
  tags: string[];
  media: string[];
  aiSummary?: string;
}

export interface MemoryTrail {
  id: string;
  userId: string;
  memories: Memory[];
  title: string;
  description?: string;
  createdAt: Date;
  isPublic: boolean;
  collaborators: string[];
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  timestamp: Date;
  expiresAt?: Date;
}

export type NotificationType = 
  | 'message' 
  | 'time_capsule_unlock' 
  | 'memory_created' 
  | 'puzzle_solved' 
  | 'heartbeat_sync' 
  | 'ai_insight' 
  | 'system' 
  | 'friend_request' 
  | 'achievement';

// Theme and UI
export interface Theme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
  };
  animations: {
    enabled: boolean;
    intensity: 'low' | 'medium' | 'high';
  };
  sounds: {
    enabled: boolean;
    volume: number;
    pack: 'default' | 'nature' | 'space' | 'minimal';
  };
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// WebSocket Events
export interface SocketEvent {
  type: string;
  data: any;
  timestamp: Date;
  userId?: string;
  chatId?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}
