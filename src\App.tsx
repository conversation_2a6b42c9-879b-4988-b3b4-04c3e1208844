import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-12">
          <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent mb-4">
            🚀 FutureChat
          </h1>
          <p className="text-xl text-gray-300">
            AI-Integrated Emotional Messaging Platform
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
            <div className="text-4xl mb-4">🧬</div>
            <h3 className="text-xl font-semibold mb-2">Emotion-Based UI</h3>
            <p className="text-gray-300">Dynamic themes that adapt to your emotional state</p>
          </div>

          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-xl font-semibold mb-2">AI Mirror Mode</h3>
            <p className="text-gray-300">Reflect on conversations with AI-powered insights</p>
          </div>

          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
            <div className="text-4xl mb-4">⏰</div>
            <h3 className="text-xl font-semibold mb-2">Time Capsules</h3>
            <p className="text-gray-300">Send messages that unlock in the future</p>
          </div>

          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
            <div className="text-4xl mb-4">🎥</div>
            <h3 className="text-xl font-semibold mb-2">Video Reactions</h3>
            <p className="text-gray-300">Record 1-3 second reaction bubbles</p>
          </div>

          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
            <div className="text-4xl mb-4">🎵</div>
            <h3 className="text-xl font-semibold mb-2">Sound Moodscapes</h3>
            <p className="text-gray-300">Emotional soundtracks for your conversations</p>
          </div>

          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
            <div className="text-4xl mb-4">🗺️</div>
            <h3 className="text-xl font-semibold mb-2">Memory Map</h3>
            <p className="text-gray-300">Explore chats across geographical locations</p>
          </div>
        </div>

        <div className="text-center">
          <button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105">
            🚀 Launch FutureChat
          </button>
          <p className="text-gray-400 mt-4">
            Coming Soon - The Future of Emotional Communication
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
