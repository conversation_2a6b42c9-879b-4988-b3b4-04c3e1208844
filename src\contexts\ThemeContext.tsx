import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Theme, EmotionalTone } from '../types';

interface ThemeContextType {
  currentTheme: Theme;
  emotionalTheme: Theme | null;
  isDarkMode: boolean;
  isEmotionalMode: boolean;
  setTheme: (theme: Theme) => void;
  setEmotionalTheme: (emotion: EmotionalTone | null) => void;
  toggleDarkMode: () => void;
  toggleEmotionalMode: () => void;
  getThemeForEmotion: (emotion: EmotionalTone) => Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

// Default themes
const defaultTheme: Theme = {
  id: 'default',
  name: 'Default Dark',
  colors: {
    primary: '#0ea5e9',
    secondary: '#d946ef',
    accent: '#f97316',
    background: '#0f172a',
    surface: '#1e293b',
    text: '#ffffff',
  },
  animations: {
    enabled: true,
    intensity: 'medium',
  },
  sounds: {
    enabled: true,
    volume: 0.5,
    pack: 'default',
  },
};

// Emotional themes based on emotions
const emotionalThemes: { [key: string]: Theme } = {
  joy: {
    id: 'joy',
    name: 'Joyful',
    colors: {
      primary: '#fbbf24',
      secondary: '#f59e0b',
      accent: '#fcd34d',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'high',
    },
    sounds: {
      enabled: true,
      volume: 0.7,
      pack: 'nature',
    },
  },
  sadness: {
    id: 'sadness',
    name: 'Melancholic',
    colors: {
      primary: '#3b82f6',
      secondary: '#1d4ed8',
      accent: '#60a5fa',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'low',
    },
    sounds: {
      enabled: true,
      volume: 0.3,
      pack: 'minimal',
    },
  },
  anger: {
    id: 'anger',
    name: 'Fiery',
    colors: {
      primary: '#ef4444',
      secondary: '#dc2626',
      accent: '#f87171',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'high',
    },
    sounds: {
      enabled: true,
      volume: 0.6,
      pack: 'default',
    },
  },
  love: {
    id: 'love',
    name: 'Romantic',
    colors: {
      primary: '#ec4899',
      secondary: '#be185d',
      accent: '#f472b6',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'medium',
    },
    sounds: {
      enabled: true,
      volume: 0.5,
      pack: 'nature',
    },
  },
  excitement: {
    id: 'excitement',
    name: 'Electric',
    colors: {
      primary: '#8b5cf6',
      secondary: '#7c3aed',
      accent: '#a78bfa',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'high',
    },
    sounds: {
      enabled: true,
      volume: 0.8,
      pack: 'space',
    },
  },
  calm: {
    id: 'calm',
    name: 'Serene',
    colors: {
      primary: '#10b981',
      secondary: '#059669',
      accent: '#34d399',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'low',
    },
    sounds: {
      enabled: true,
      volume: 0.4,
      pack: 'nature',
    },
  },
  anxiety: {
    id: 'anxiety',
    name: 'Restless',
    colors: {
      primary: '#f59e0b',
      secondary: '#d97706',
      accent: '#fbbf24',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'medium',
    },
    sounds: {
      enabled: true,
      volume: 0.3,
      pack: 'minimal',
    },
  },
  nostalgia: {
    id: 'nostalgia',
    name: 'Nostalgic',
    colors: {
      primary: '#a855f7',
      secondary: '#9333ea',
      accent: '#c084fc',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#ffffff',
    },
    animations: {
      enabled: true,
      intensity: 'low',
    },
    sounds: {
      enabled: true,
      volume: 0.4,
      pack: 'minimal',
    },
  },
};

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(defaultTheme);
  const [emotionalTheme, setEmotionalTheme] = useState<Theme | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isEmotionalMode, setIsEmotionalMode] = useState(true);

  useEffect(() => {
    // Load theme preferences from localStorage
    const savedTheme = localStorage.getItem('theme');
    const savedDarkMode = localStorage.getItem('darkMode');
    const savedEmotionalMode = localStorage.getItem('emotionalMode');

    if (savedTheme) {
      try {
        const theme = JSON.parse(savedTheme);
        setCurrentTheme(theme);
      } catch (error) {
        console.error('Error loading saved theme:', error);
      }
    }

    if (savedDarkMode !== null) {
      setIsDarkMode(savedDarkMode === 'true');
    }

    if (savedEmotionalMode !== null) {
      setIsEmotionalMode(savedEmotionalMode === 'true');
    }
  }, []);

  useEffect(() => {
    // Apply theme to CSS variables
    const activeTheme = isEmotionalMode && emotionalTheme ? emotionalTheme : currentTheme;
    
    const root = document.documentElement;
    root.style.setProperty('--color-primary', activeTheme.colors.primary);
    root.style.setProperty('--color-secondary', activeTheme.colors.secondary);
    root.style.setProperty('--color-accent', activeTheme.colors.accent);
    root.style.setProperty('--color-background', activeTheme.colors.background);
    root.style.setProperty('--color-surface', activeTheme.colors.surface);
    root.style.setProperty('--color-text', activeTheme.colors.text);

    // Apply animation intensity
    const animationClass = `animation-${activeTheme.animations.intensity}`;
    document.body.className = document.body.className.replace(/animation-\w+/g, '');
    if (activeTheme.animations.enabled) {
      document.body.classList.add(animationClass);
    }
  }, [currentTheme, emotionalTheme, isEmotionalMode]);

  const setTheme = (theme: Theme) => {
    setCurrentTheme(theme);
    localStorage.setItem('theme', JSON.stringify(theme));
  };

  const setEmotionalThemeHandler = (emotion: EmotionalTone | null) => {
    if (emotion) {
      const theme = getThemeForEmotion(emotion);
      setEmotionalTheme(theme);
    } else {
      setEmotionalTheme(null);
    }
  };

  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());
  };

  const toggleEmotionalMode = () => {
    const newEmotionalMode = !isEmotionalMode;
    setIsEmotionalMode(newEmotionalMode);
    localStorage.setItem('emotionalMode', newEmotionalMode.toString());
  };

  const getThemeForEmotion = (emotion: EmotionalTone): Theme => {
    const baseTheme = emotionalThemes[emotion.primary] || defaultTheme;
    
    // Adjust theme based on emotion intensity
    const intensityMultiplier = emotion.intensity;
    
    return {
      ...baseTheme,
      animations: {
        ...baseTheme.animations,
        intensity: intensityMultiplier > 0.7 ? 'high' : 
                  intensityMultiplier > 0.4 ? 'medium' : 'low',
      },
      sounds: {
        ...baseTheme.sounds,
        volume: baseTheme.sounds.volume * intensityMultiplier,
      },
    };
  };

  const value: ThemeContextType = {
    currentTheme,
    emotionalTheme,
    isDarkMode,
    isEmotionalMode,
    setTheme,
    setEmotionalTheme: setEmotionalThemeHandler,
    toggleDarkMode,
    toggleEmotionalMode,
    getThemeForEmotion,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
