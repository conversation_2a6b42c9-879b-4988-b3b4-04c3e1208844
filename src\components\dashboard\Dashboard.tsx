import React from 'react';
import { motion } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  HeartIcon,
  SparklesIcon,
  MapPinIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../hooks/useAuth';
import { useChat } from '../../contexts/ChatContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { chats } = useChat();

  const features = [
    {
      icon: ChatBubbleLeftRightIcon,
      title: 'Emotional Chat',
      description: 'Experience conversations that adapt to your emotions',
      color: 'from-primary-500 to-primary-600',
      href: '/chat'
    },
    {
      icon: HeartIcon,
      title: 'Heartbeat Sync',
      description: 'Connect with others through synchronized heartbeats',
      color: 'from-red-500 to-pink-600',
      href: '/chat'
    },
    {
      icon: SparklesIcon,
      title: 'AI Mirror',
      description: 'Reflect on your thoughts with AI-powered insights',
      color: 'from-purple-500 to-purple-600',
      href: '/chat'
    },
    {
      icon: MapPinIcon,
      title: 'Memory Map',
      description: 'Explore your conversations across time and space',
      color: 'from-green-500 to-green-600',
      href: '/memory/map'
    },
    {
      icon: ClockIcon,
      title: 'Time Capsules',
      description: 'Send messages to the future',
      color: 'from-blue-500 to-blue-600',
      href: '/chat'
    },
    {
      icon: StarIcon,
      title: 'Stargazer Mode',
      description: 'Share the night sky with your loved ones',
      color: 'from-indigo-500 to-indigo-600',
      href: '/chat'
    }
  ];

  const stats = [
    { label: 'Active Chats', value: chats.length },
    { label: 'Messages Today', value: 42 },
    { label: 'Emotional Moments', value: 18 },
    { label: 'Memories Created', value: 7 }
  ];

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="max-w-7xl mx-auto">
        {/* Welcome Section */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-display font-bold gradient-text mb-2">
            Welcome back, {user?.displayName}!
          </h1>
          <p className="text-white/70 text-lg">
            Ready to explore the future of emotional communication?
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="glass rounded-xl p-6 text-center"
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="text-3xl font-bold gradient-text mb-2">
                {stat.value}
              </div>
              <div className="text-white/60 text-sm">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-display font-bold text-white mb-6">
            Explore Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                className="glass rounded-xl p-6 cursor-pointer group"
                whileHover={{ scale: 1.05, y: -5 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
              >
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-white/70 text-sm">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          className="glass rounded-xl p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <h2 className="text-2xl font-display font-bold text-white mb-6">
            Recent Activity
          </h2>
          <div className="space-y-4">
            {chats.slice(0, 3).map((chat, index) => (
              <motion.div
                key={chat.id}
                className="flex items-center space-x-4 p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer"
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                  <span className="text-white font-semibold">
                    {chat.name ? chat.name[0].toUpperCase() : 'C'}
                  </span>
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">
                    {chat.name || 'Unnamed Chat'}
                  </h3>
                  <p className="text-white/60 text-sm">
                    {chat.lastMessage?.content || 'No messages yet'}
                  </p>
                </div>
                <div className="text-white/40 text-xs">
                  {chat.lastMessage?.timestamp ?
                    new Date(chat.lastMessage.timestamp).toLocaleDateString() :
                    'New'
                  }
                </div>
              </motion.div>
            ))}
            {chats.length === 0 && (
              <div className="text-center py-8">
                <ChatBubbleLeftRightIcon className="w-16 h-16 text-white/20 mx-auto mb-4" />
                <p className="text-white/60">
                  No chats yet. Start your first conversation!
                </p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          className="mt-8 flex flex-wrap gap-4 justify-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <motion.button
            className="btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Start New Chat
          </motion.button>
          <motion.button
            className="btn-secondary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Explore Memories
          </motion.button>
          <motion.button
            className="glass rounded-lg py-2 px-4 text-white hover:bg-white/20 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            AI Tarot Reading
          </motion.button>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
