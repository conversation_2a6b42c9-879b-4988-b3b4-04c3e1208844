import React from 'react';
import { motion } from 'framer-motion';
import { 
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../hooks/useAuth';

interface NavbarProps {
  onMenuClick: () => void;
  sidebarOpen: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ onMenuClick, sidebarOpen }) => {
  const { user, logout } = useAuth();

  return (
    <motion.nav
      className="glass border-b border-white/10 px-6 py-4 flex items-center justify-between"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Left Side */}
      <div className="flex items-center space-x-4">
        <motion.button
          onClick={onMenuClick}
          className="p-2 rounded-lg hover:bg-white/10 transition-colors lg:hidden"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Bars3Icon className="w-6 h-6 text-white" />
        </motion.button>
        
        <div className="hidden lg:block">
          <h1 className="text-xl font-display font-bold gradient-text">
            FutureChat
          </h1>
        </div>
      </div>

      {/* Center - Search or Status */}
      <div className="flex-1 max-w-md mx-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations..."
            className="input-glass w-full pl-4 pr-10"
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          </div>
        </div>
      </div>

      {/* Right Side */}
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <motion.button
          className="p-2 rounded-lg hover:bg-white/10 transition-colors relative"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <BellIcon className="w-6 h-6 text-white" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full" />
        </motion.button>

        {/* Settings */}
        <motion.button
          className="p-2 rounded-lg hover:bg-white/10 transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <Cog6ToothIcon className="w-6 h-6 text-white" />
        </motion.button>

        {/* User Menu */}
        <div className="relative group">
          <motion.button
            className="flex items-center space-x-2 p-2 rounded-lg hover:bg-white/10 transition-colors"
            whileHover={{ scale: 1.05 }}
          >
            {user?.photoURL ? (
              <img
                src={user.photoURL}
                alt={user.displayName}
                className="w-8 h-8 rounded-full"
              />
            ) : (
              <UserCircleIcon className="w-8 h-8 text-white" />
            )}
            <span className="text-white font-medium hidden md:block">
              {user?.displayName}
            </span>
          </motion.button>

          {/* Dropdown Menu */}
          <div className="absolute right-0 top-full mt-2 w-48 glass rounded-lg py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            <button className="w-full text-left px-4 py-2 text-white hover:bg-white/10 transition-colors">
              Profile
            </button>
            <button className="w-full text-left px-4 py-2 text-white hover:bg-white/10 transition-colors">
              Settings
            </button>
            <hr className="border-white/20 my-2" />
            <button
              onClick={logout}
              className="w-full text-left px-4 py-2 text-red-400 hover:bg-white/10 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default Navbar;
