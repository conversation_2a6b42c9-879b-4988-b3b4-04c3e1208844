import React from 'react';
import { motion } from 'framer-motion';
import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  MapPinIcon,
  Cog6ToothIcon,
  SparklesIcon,
  HeartIcon,
  StarIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useChat } from '../../contexts/ChatContext';

interface SidebarProps {
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onClose }) => {
  const { chats } = useChat();

  const navigationItems = [
    { icon: HomeIcon, label: 'Dashboard', path: '/' },
    { icon: ChatBubbleLeftRightIcon, label: 'Chat', path: '/chat' },
    { icon: ClockIcon, label: 'Memory Timeline', path: '/memory/timeline' },
    { icon: MapPinIcon, label: 'Memory Map', path: '/memory/map' },
    { icon: SparklesIcon, label: 'AI Features', path: '/ai' },
    { icon: HeartIcon, label: 'Emotions', path: '/emotions' },
    { icon: StarIcon, label: 'Stargazer', path: '/stargazer' },
    { icon: Cog6ToothIcon, label: 'Settings', path: '/settings' },
  ];

  return (
    <div className="h-full glass border-r border-white/10 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-white/10 flex items-center justify-between">
        <h2 className="text-xl font-display font-bold gradient-text">
          FutureChat
        </h2>
        <motion.button
          onClick={onClose}
          className="p-2 rounded-lg hover:bg-white/10 transition-colors lg:hidden"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <XMarkIcon className="w-5 h-5 text-white" />
        </motion.button>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-sm font-medium text-white/60 uppercase tracking-wider mb-4">
            Navigation
          </h3>
          <nav className="space-y-2">
            {navigationItems.map((item, index) => (
              <motion.div
                key={item.path}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-gradient-to-r from-primary-500/20 to-secondary-500/20 text-white border border-primary-500/30'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`
                  }
                  onClick={() => window.innerWidth < 1024 && onClose()}
                >
                  <item.icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </NavLink>
              </motion.div>
            ))}
          </nav>
        </div>

        {/* Recent Chats */}
        <div className="p-4 border-t border-white/10">
          <h3 className="text-sm font-medium text-white/60 uppercase tracking-wider mb-4">
            Recent Chats
          </h3>
          <div className="space-y-2">
            {chats.slice(0, 5).map((chat, index) => (
              <motion.div
                key={chat.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
              >
                <NavLink
                  to={`/chat/${chat.id}`}
                  className="flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors group"
                  onClick={() => window.innerWidth < 1024 && onClose()}
                >
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-sm font-semibold">
                      {chat.name ? chat.name[0].toUpperCase() : 'C'}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">
                      {chat.name || 'Unnamed Chat'}
                    </p>
                    <p className="text-white/60 text-sm truncate">
                      {chat.lastMessage?.content || 'No messages yet'}
                    </p>
                  </div>
                  {chat.lastMessage && (
                    <div className="w-2 h-2 bg-primary-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                  )}
                </NavLink>
              </motion.div>
            ))}
            {chats.length === 0 && (
              <div className="text-center py-8">
                <ChatBubbleLeftRightIcon className="w-12 h-12 text-white/20 mx-auto mb-2" />
                <p className="text-white/60 text-sm">
                  No chats yet
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-white/10">
        <motion.button
          className="w-full btn-primary"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          New Chat
        </motion.button>
      </div>
    </div>
  );
};

export default Sidebar;
