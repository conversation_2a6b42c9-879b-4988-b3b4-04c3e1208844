import React from 'react';
import { motion } from 'framer-motion';
import { MapPinIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

const MemoryMap: React.FC = () => {
  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-display font-bold gradient-text mb-2">
            Memory Map
          </h1>
          <p className="text-white/70 text-lg">
            Explore your conversations across the world
          </p>
        </motion.div>

        {/* Map Placeholder */}
        <motion.div
          className="glass rounded-xl p-8 h-96 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="text-center">
            <GlobeAltIcon className="w-24 h-24 text-white/20 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              Interactive Map Coming Soon
            </h3>
            <p className="text-white/60">
              Visualize your conversations on a beautiful world map
            </p>
          </div>
        </motion.div>

        {/* Location Stats */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="glass rounded-xl p-6 text-center">
            <MapPinIcon className="w-8 h-8 text-primary-400 mx-auto mb-2" />
            <div className="text-2xl font-bold gradient-text mb-1">12</div>
            <div className="text-white/60 text-sm">Locations Visited</div>
          </div>
          <div className="glass rounded-xl p-6 text-center">
            <GlobeAltIcon className="w-8 h-8 text-secondary-400 mx-auto mb-2" />
            <div className="text-2xl font-bold gradient-text mb-1">5</div>
            <div className="text-white/60 text-sm">Countries</div>
          </div>
          <div className="glass rounded-xl p-6 text-center">
            <MapPinIcon className="w-8 h-8 text-accent-400 mx-auto mb-2" />
            <div className="text-2xl font-bold gradient-text mb-1">847</div>
            <div className="text-white/60 text-sm">Miles Traveled</div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default MemoryMap;
