import React from 'react';
import { motion } from 'framer-motion';
import { 
  Cog6ToothIcon,
  BellIcon,
  PaintBrushIcon,
  HeartIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

const Settings: React.FC = () => {
  const settingsSections = [
    {
      icon: PaintBrushIcon,
      title: 'Appearance',
      description: 'Customize your theme and visual preferences',
      settings: [
        { label: 'Dark Mode', type: 'toggle', value: true },
        { label: 'Emotional UI', type: 'toggle', value: true },
        { label: 'Animation Intensity', type: 'select', value: 'medium' },
      ]
    },
    {
      icon: BellIcon,
      title: 'Notifications',
      description: 'Manage your notification preferences',
      settings: [
        { label: 'Push Notifications', type: 'toggle', value: true },
        { label: 'Sound Alerts', type: 'toggle', value: true },
        { label: 'Email Notifications', type: 'toggle', value: false },
      ]
    },
    {
      icon: HeartIcon,
      title: 'Emotional Features',
      description: 'Configure emotion-based features',
      settings: [
        { label: 'Emotion Analysis', type: 'toggle', value: true },
        { label: 'Mood-based Themes', type: 'toggle', value: true },
        { label: 'Heartbeat Sync', type: 'toggle', value: false },
      ]
    },
    {
      icon: SparklesIcon,
      title: 'AI Features',
      description: 'Control AI-powered functionality',
      settings: [
        { label: 'AI Mirror Mode', type: 'toggle', value: false },
        { label: 'Predictive Text', type: 'toggle', value: true },
        { label: 'Dream Chat', type: 'toggle', value: true },
      ]
    },
  ];

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-display font-bold gradient-text mb-2">
            Settings
          </h1>
          <p className="text-white/70 text-lg">
            Customize your FutureChat experience
          </p>
        </motion.div>

        <div className="space-y-6">
          {settingsSections.map((section, index) => (
            <motion.div
              key={section.title}
              className="glass rounded-xl p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center">
                  <section.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">
                    {section.title}
                  </h2>
                  <p className="text-white/60 text-sm">
                    {section.description}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                {section.settings.map((setting, settingIndex) => (
                  <div
                    key={setting.label}
                    className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0"
                  >
                    <span className="text-white font-medium">
                      {setting.label}
                    </span>
                    
                    {setting.type === 'toggle' && (
                      <motion.button
                        className={`relative w-12 h-6 rounded-full transition-colors ${
                          setting.value ? 'bg-primary-500' : 'bg-white/20'
                        }`}
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          className="absolute top-1 w-4 h-4 bg-white rounded-full shadow-lg"
                          animate={{
                            x: setting.value ? 24 : 4
                          }}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      </motion.button>
                    )}
                    
                    {setting.type === 'select' && (
                      <select className="input-glass py-1 px-3 text-sm">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                      </select>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Account Section */}
        <motion.div
          className="glass rounded-xl p-6 mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
              <Cog6ToothIcon className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">
                Account
              </h2>
              <p className="text-white/60 text-sm">
                Manage your account settings
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <motion.button
              className="w-full text-left py-3 px-4 rounded-lg hover:bg-white/10 transition-colors text-white"
              whileHover={{ x: 5 }}
            >
              Export Data
            </motion.button>
            <motion.button
              className="w-full text-left py-3 px-4 rounded-lg hover:bg-white/10 transition-colors text-white"
              whileHover={{ x: 5 }}
            >
              Privacy Settings
            </motion.button>
            <motion.button
              className="w-full text-left py-3 px-4 rounded-lg hover:bg-red-500/20 transition-colors text-red-400"
              whileHover={{ x: 5 }}
            >
              Delete Account
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Settings;
