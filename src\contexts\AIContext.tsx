import React, { createContext, useContext, useState, ReactNode } from 'react';
import { EmotionalTone, AIResponse, TarotReading, TarotCard, Message } from '../types';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

interface AIContextType {
  analyzeEmotion: (text: string) => Promise<EmotionalTone | null>;
  generateMirrorResponse: (messages: Message[]) => Promise<string | null>;
  generatePredictiveText: (context: string) => Promise<string[]>;
  generateTarotReading: (question: string) => Promise<TarotReading | null>;
  generateDreamChatResponse: (message: string) => Promise<string | null>;
  generateParallelUniverseChat: (messages: Message[], scenario: string) => Promise<Message[]>;
  generateAISummary: (messages: Message[]) => Promise<string | null>;
  isProcessing: boolean;
}

const AIContext = createContext<AIContextType | undefined>(undefined);

export const useAI = () => {
  const context = useContext(AIContext);
  if (context === undefined) {
    throw new Error('useAI must be used within an AIProvider');
  }
  return context;
};

interface AIProviderProps {
  children: ReactNode;
}

// Mock tarot cards data
const tarotCards: TarotCard[] = [
  {
    name: "The Fool",
    number: 0,
    image: "/tarot/fool.jpg",
    meaning: {
      upright: "New beginnings, innocence, spontaneity, free spirit",
      reversed: "Recklessness, taken advantage of, inconsideration"
    },
    keywords: ["journey", "adventure", "new start", "potential"]
  },
  {
    name: "The Magician",
    number: 1,
    image: "/tarot/magician.jpg",
    meaning: {
      upright: "Manifestation, resourcefulness, power, inspired action",
      reversed: "Manipulation, poor planning, untapped talents"
    },
    keywords: ["power", "skill", "concentration", "action"]
  },
  {
    name: "The High Priestess",
    number: 2,
    image: "/tarot/high-priestess.jpg",
    meaning: {
      upright: "Intuition, sacred knowledge, divine feminine, subconscious mind",
      reversed: "Secrets, disconnected from intuition, withdrawal"
    },
    keywords: ["intuition", "mystery", "inner voice", "wisdom"]
  },
  // Add more cards as needed...
];

export const AIProvider: React.FC<AIProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock OpenAI API call - replace with actual implementation
  const callOpenAI = async (prompt: string, maxTokens: number = 150): Promise<string | null> => {
    try {
      // This is a mock implementation
      // In production, you would call the actual OpenAI API
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // Return mock responses based on prompt content
      if (prompt.includes('emotion')) {
        return JSON.stringify({
          primary: 'joy',
          secondary: 'excitement',
          intensity: 0.8,
          confidence: 0.9,
          keywords: ['happy', 'excited', 'positive'],
          sentiment: 'positive',
          energy: 'high'
        });
      }
      
      if (prompt.includes('mirror')) {
        return "I understand you're feeling reflective right now. Sometimes it's good to look inward and consider our thoughts and emotions.";
      }
      
      if (prompt.includes('dream')) {
        return "In the gentle realm of dreams, your message carries the whisper of peaceful thoughts. Rest well, dear dreamer.";
      }
      
      if (prompt.includes('summary')) {
        return "This conversation has been filled with meaningful exchanges, touching on themes of connection, understanding, and shared experiences.";
      }
      
      return "I'm here to help you explore your thoughts and feelings in this moment.";
    } catch (error) {
      console.error('AI API Error:', error);
      return null;
    }
  };

  const analyzeEmotion = async (text: string): Promise<EmotionalTone | null> => {
    if (!text.trim()) return null;

    setIsProcessing(true);
    try {
      const prompt = `Analyze the emotional tone of this text and return a JSON object with the following structure:
      {
        "primary": "emotion_type",
        "secondary": "emotion_type",
        "intensity": 0.0-1.0,
        "confidence": 0.0-1.0,
        "keywords": ["word1", "word2"],
        "sentiment": "positive|negative|neutral",
        "energy": "high|medium|low"
      }
      
      Text to analyze: "${text}"`;

      const response = await callOpenAI(prompt);
      if (response) {
        return JSON.parse(response) as EmotionalTone;
      }
      return null;
    } catch (error) {
      console.error('Error analyzing emotion:', error);
      toast.error('Failed to analyze emotion');
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  const generateMirrorResponse = async (messages: Message[]): Promise<string | null> => {
    if (!user || messages.length === 0) return null;

    setIsProcessing(true);
    try {
      const userMessages = messages
        .filter(m => m.senderId === user.id)
        .slice(-5)
        .map(m => m.content)
        .join('\n');

      const prompt = `As an AI mirror reflecting the user's communication style and emotional patterns, 
      respond to their recent messages in their own tone and style. Be empathetic and insightful.
      
      Recent messages: "${userMessages}"
      
      Provide a thoughtful mirror response:`;

      const response = await callOpenAI(prompt, 200);
      return response;
    } catch (error) {
      console.error('Error generating mirror response:', error);
      toast.error('Failed to generate mirror response');
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  const generatePredictiveText = async (context: string): Promise<string[]> => {
    if (!context.trim()) return [];

    setIsProcessing(true);
    try {
      const prompt = `Based on this conversation context, suggest 3 possible next messages the user might want to send:
      
      Context: "${context}"
      
      Return as a JSON array of strings:`;

      const response = await callOpenAI(prompt, 100);
      if (response) {
        return JSON.parse(response) as string[];
      }
      return [];
    } catch (error) {
      console.error('Error generating predictive text:', error);
      return [];
    } finally {
      setIsProcessing(false);
    }
  };

  const generateTarotReading = async (question: string): Promise<TarotReading | null> => {
    if (!question.trim() || !user) return null;

    setIsProcessing(true);
    try {
      // Select a random tarot card
      const randomCard = tarotCards[Math.floor(Math.random() * tarotCards.length)];
      
      const prompt = `Provide a mystical tarot interpretation for this question using ${randomCard.name}:
      
      Question: "${question}"
      Card: ${randomCard.name}
      Meaning: ${randomCard.meaning.upright}
      
      Provide a thoughtful, mystical interpretation:`;

      const interpretation = await callOpenAI(prompt, 200);
      
      if (interpretation) {
        return {
          id: Date.now().toString(),
          question,
          card: randomCard,
          interpretation,
          timestamp: new Date(),
          userId: user.id,
        };
      }
      return null;
    } catch (error) {
      console.error('Error generating tarot reading:', error);
      toast.error('Failed to generate tarot reading');
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  const generateDreamChatResponse = async (message: string): Promise<string | null> => {
    if (!message.trim()) return null;

    setIsProcessing(true);
    try {
      const prompt = `Respond to this message in a dreamy, calming, bedtime-appropriate tone. 
      Use gentle language and peaceful imagery:
      
      Message: "${message}"
      
      Dream response:`;

      const response = await callOpenAI(prompt, 150);
      return response;
    } catch (error) {
      console.error('Error generating dream chat response:', error);
      toast.error('Failed to generate dream response');
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  const generateParallelUniverseChat = async (
    messages: Message[], 
    scenario: string
  ): Promise<Message[]> => {
    if (messages.length === 0) return [];

    setIsProcessing(true);
    try {
      const conversationContext = messages
        .slice(-10)
        .map(m => `${m.senderId}: ${m.content}`)
        .join('\n');

      const prompt = `Reimagine this conversation in a parallel universe where ${scenario}:
      
      Original conversation:
      ${conversationContext}
      
      Generate 3-5 alternative messages showing how this conversation might have gone differently.
      Return as JSON array with format: [{"senderId": "user_id", "content": "message"}]`;

      const response = await callOpenAI(prompt, 300);
      if (response) {
        const alternativeMessages = JSON.parse(response);
        return alternativeMessages.map((msg: any, index: number) => ({
          id: `parallel_${Date.now()}_${index}`,
          chatId: 'parallel_universe',
          senderId: msg.senderId,
          content: msg.content,
          type: 'text' as const,
          timestamp: new Date(),
          reactions: [],
          attachments: [],
          metadata: {
            readBy: {},
            deliveredTo: {},
          },
        }));
      }
      return [];
    } catch (error) {
      console.error('Error generating parallel universe chat:', error);
      toast.error('Failed to generate parallel universe chat');
      return [];
    } finally {
      setIsProcessing(false);
    }
  };

  const generateAISummary = async (messages: Message[]): Promise<string | null> => {
    if (messages.length === 0) return null;

    setIsProcessing(true);
    try {
      const conversationText = messages
        .map(m => m.content)
        .join(' ');

      const prompt = `Summarize this conversation, highlighting key themes, emotions, and memorable moments:
      
      Conversation: "${conversationText}"
      
      Summary:`;

      const response = await callOpenAI(prompt, 200);
      return response;
    } catch (error) {
      console.error('Error generating AI summary:', error);
      toast.error('Failed to generate summary');
      return null;
    } finally {
      setIsProcessing(false);
    }
  };

  const value: AIContextType = {
    analyzeEmotion,
    generateMirrorResponse,
    generatePredictiveText,
    generateTarotReading,
    generateDreamChatResponse,
    generateParallelUniverseChat,
    generateAISummary,
    isProcessing,
  };

  return (
    <AIContext.Provider value={value}>
      {children}
    </AIContext.Provider>
  );
};
